using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SchoolManager.Data;
using SchoolManager.Models;
using System.Diagnostics;

namespace SchoolManager.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly SchoolContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public HomeController(ILogger<HomeController> logger, SchoolContext context, UserManager<ApplicationUser> userManager)
        {
            _logger = logger;
            _context = context;
            _userManager = userManager;
        }

        public async Task<IActionResult> Index()
        {
            if (!User.Identity?.IsAuthenticated ?? true)
            {
                return View("Welcome");
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var roles = await _userManager.GetRolesAsync(user);
            var userRole = roles.FirstOrDefault();

            var dashboardData = new DashboardViewModel
            {
                UserName = user.FullName,
                UserRole = userRole ?? "User"
            };

            // Load role-specific dashboard data
            switch (userRole?.ToLower())
            {
                case "admin":
                    await LoadAdminDashboardData(dashboardData);
                    break;
                case "teacher":
                    await LoadTeacherDashboardData(dashboardData, user.Id);
                    break;
                case "student":
                    await LoadStudentDashboardData(dashboardData, user.Id);
                    break;
                case "parent":
                    await LoadParentDashboardData(dashboardData, user.Id);
                    break;
            }

            return View("Dashboard", dashboardData);
        }

        private async Task LoadAdminDashboardData(DashboardViewModel model)
        {
            model.TotalStudents = await _context.Students.CountAsync(s => s.Status == StudentStatus.Active);
            model.TotalTeachers = await _context.Teachers.CountAsync(t => t.Status == TeacherStatus.Active);
            model.TotalClasses = await _context.Classes.CountAsync(c => c.IsActive);
            model.TotalSubjects = await _context.Subjects.CountAsync(s => s.IsActive);

            // Recent activities
            model.RecentActivities = await _context.UserActivities
                .Include(ua => ua.User)
                .OrderByDescending(ua => ua.Timestamp)
                .Take(10)
                .Select(ua => $"{ua.User.FullName} - {ua.Action} ({ua.Timestamp:HH:mm})")
                .ToListAsync();

            // Financial summary
            var currentMonth = DateTime.Now.Month;
            var currentYear = DateTime.Now.Year;
            
            model.MonthlyRevenue = await _context.Payments
                .Where(p => p.Status == PaymentStatus.Paid && 
                           p.PaidDate.HasValue && 
                           p.PaidDate.Value.Month == currentMonth && 
                           p.PaidDate.Value.Year == currentYear)
                .SumAsync(p => p.NetAmount);

            model.PendingPayments = await _context.Payments
                .Where(p => p.Status == PaymentStatus.Pending)
                .SumAsync(p => p.NetAmount);

            // Attendance summary for today
            var today = DateTime.Today;
            var todayAttendance = await _context.Attendances
                .Where(a => a.Date.Date == today)
                .GroupBy(a => a.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            model.TodayPresentCount = todayAttendance.FirstOrDefault(a => a.Status == AttendanceStatus.Present)?.Count ?? 0;
            model.TodayAbsentCount = todayAttendance.FirstOrDefault(a => a.Status == AttendanceStatus.Absent)?.Count ?? 0;
        }

        private async Task LoadTeacherDashboardData(DashboardViewModel model, string userId)
        {
            var teacher = await _context.Teachers
                .Include(t => t.SubjectTeachers)
                .ThenInclude(st => st.Class)
                .FirstOrDefaultAsync(t => t.UserId == userId);

            if (teacher != null)
            {
                model.MyClasses = teacher.SubjectTeachers
                    .Where(st => st.IsActive)
                    .Select(st => st.Class.FullName)
                    .Distinct()
                    .ToList();

                // Today's schedule
                var today = DateTime.Today.DayOfWeek;
                model.TodaySchedule = await _context.Schedules
                    .Include(s => s.Subject)
                    .Include(s => s.Class)
                    .Where(s => s.TeacherId == teacher.Id && s.DayOfWeek == today && s.IsActive)
                    .OrderBy(s => s.StartTime)
                    .Select(s => $"{s.TimeSlot} - {s.Subject.Name} ({s.Class.FullName})")
                    .ToListAsync();

                // Pending grades
                model.PendingGrades = await _context.Grades
                    .Include(g => g.Student)
                    .ThenInclude(s => s.User)
                    .Include(g => g.Subject)
                    .Where(g => g.TeacherId == teacher.Id && g.Status == GradeStatus.InProgress)
                    .CountAsync();
            }
        }

        private async Task LoadStudentDashboardData(DashboardViewModel model, string userId)
        {
            var student = await _context.Students
                .Include(s => s.Class)
                .Include(s => s.Grades)
                .ThenInclude(g => g.Subject)
                .FirstOrDefaultAsync(s => s.UserId == userId);

            if (student != null)
            {
                model.MyClass = student.Class.FullName;
                
                // Current semester GPA
                var currentSemester = "الفصل الأول"; // This should be dynamic
                var currentGrades = student.Grades.Where(g => g.Semester == currentSemester).ToList();
                
                if (currentGrades.Any())
                {
                    model.CurrentGPA = currentGrades.Average(g => g.GPA);
                }

                // Recent grades
                model.RecentGrades = currentGrades
                    .OrderByDescending(g => g.LastModified)
                    .Take(5)
                    .Select(g => $"{g.Subject.Name}: {g.TotalScore:F1} ({g.LetterGrade})")
                    .ToList();

                // Attendance percentage
                var thisMonth = DateTime.Now.Month;
                var thisYear = DateTime.Now.Year;
                var monthlyAttendance = await _context.Attendances
                    .Where(a => a.StudentId == student.Id && 
                               a.Date.Month == thisMonth && 
                               a.Date.Year == thisYear)
                    .ToListAsync();

                if (monthlyAttendance.Any())
                {
                    var presentDays = monthlyAttendance.Count(a => a.Status == AttendanceStatus.Present);
                    model.AttendancePercentage = (double)presentDays / monthlyAttendance.Count * 100;
                }

                // Pending payments
                model.PendingPaymentAmount = await _context.Payments
                    .Where(p => p.StudentId == student.Id && p.Status == PaymentStatus.Pending)
                    .SumAsync(p => p.NetAmount);
            }
        }

        private async Task LoadParentDashboardData(DashboardViewModel model, string userId)
        {
            // For parent dashboard, we would need to link parents to students
            // This is a simplified version
            model.UserRole = "Parent";
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }

    public class DashboardViewModel
    {
        public string UserName { get; set; } = string.Empty;
        public string UserRole { get; set; } = string.Empty;

        // Admin Dashboard
        public int TotalStudents { get; set; }
        public int TotalTeachers { get; set; }
        public int TotalClasses { get; set; }
        public int TotalSubjects { get; set; }
        public decimal MonthlyRevenue { get; set; }
        public decimal PendingPayments { get; set; }
        public int TodayPresentCount { get; set; }
        public int TodayAbsentCount { get; set; }
        public List<string> RecentActivities { get; set; } = new List<string>();

        // Teacher Dashboard
        public List<string> MyClasses { get; set; } = new List<string>();
        public List<string> TodaySchedule { get; set; } = new List<string>();
        public int PendingGrades { get; set; }

        // Student Dashboard
        public string MyClass { get; set; } = string.Empty;
        public double CurrentGPA { get; set; }
        public List<string> RecentGrades { get; set; } = new List<string>();
        public double AttendancePercentage { get; set; }
        public decimal PendingPaymentAmount { get; set; }
    }

    public class ErrorViewModel
    {
        public string? RequestId { get; set; }
        public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);
    }
}
