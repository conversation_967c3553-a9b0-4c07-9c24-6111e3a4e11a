using iTextSharp.text;
using iTextSharp.text.pdf;
using Microsoft.EntityFrameworkCore;
using SchoolManager.Data;
using SchoolManager.Models;
using System.Text;

namespace SchoolManager.Services
{
    public class ReportService : IReportService
    {
        private readonly SchoolContext _context;
        private readonly IConfiguration _configuration;

        public ReportService(SchoolContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }

        public async Task<byte[]> GenerateStudentReportCardAsync(int studentId, string semester, string academicYear)
        {
            var student = await _context.Students
                .Include(s => s.User)
                .Include(s => s.Class)
                .FirstOrDefaultAsync(s => s.Id == studentId);

            if (student == null)
                throw new ArgumentException("Student not found");

            var grades = await _context.Grades
                .Include(g => g.Subject)
                .Include(g => g.Teacher)
                .ThenInclude(t => t.User)
                .Where(g => g.StudentId == studentId && g.Semester == semester && g.AcademicYear == academicYear)
                .ToListAsync();

            using var stream = new MemoryStream();
            var document = new Document(PageSize.A4);
            var writer = PdfWriter.GetInstance(document, stream);

            document.Open();

            // Add school header
            var schoolName = _configuration["SchoolSettings:SchoolName"] ?? "School Management System";
            var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 16);
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);
            var normalFont = FontFactory.GetFont(FontFactory.HELVETICA, 10);

            document.Add(new Paragraph(schoolName, titleFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(new Paragraph("Student Report Card", headerFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(new Paragraph($"Academic Year: {academicYear} - Semester: {semester}", normalFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(new Paragraph(" "));

            // Student information
            document.Add(new Paragraph($"Student Name: {student.User.FullName}", normalFont));
            document.Add(new Paragraph($"Student Number: {student.StudentNumber}", normalFont));
            document.Add(new Paragraph($"Class: {student.Class.FullName}", normalFont));
            document.Add(new Paragraph(" "));

            // Grades table
            var table = new PdfPTable(6) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 3, 2, 2, 2, 2, 2 });

            // Table headers
            table.AddCell(new PdfPCell(new Phrase("Subject", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
            table.AddCell(new PdfPCell(new Phrase("Attendance", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
            table.AddCell(new PdfPCell(new Phrase("Assignment", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
            table.AddCell(new PdfPCell(new Phrase("Midterm", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
            table.AddCell(new PdfPCell(new Phrase("Final", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
            table.AddCell(new PdfPCell(new Phrase("Total", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });

            decimal totalGPA = 0;
            int subjectCount = 0;

            foreach (var grade in grades)
            {
                table.AddCell(new Phrase(grade.Subject.Name, normalFont));
                table.AddCell(new Phrase(grade.AttendanceScore?.ToString("F1") ?? "-", normalFont));
                table.AddCell(new Phrase(grade.AssignmentScore?.ToString("F1") ?? "-", normalFont));
                table.AddCell(new Phrase(grade.MidtermScore?.ToString("F1") ?? "-", normalFont));
                table.AddCell(new Phrase(grade.FinalScore?.ToString("F1") ?? "-", normalFont));
                table.AddCell(new Phrase($"{grade.TotalScore:F1} ({grade.LetterGrade})", normalFont));

                totalGPA += (decimal)grade.GPA;
                subjectCount++;
            }

            document.Add(table);
            document.Add(new Paragraph(" "));

            // Summary
            var overallGPA = subjectCount > 0 ? totalGPA / subjectCount : 0;
            document.Add(new Paragraph($"Overall GPA: {overallGPA:F2}", headerFont));
            document.Add(new Paragraph($"Generated on: {DateTime.Now:yyyy/MM/dd HH:mm}", normalFont));

            document.Close();
            return stream.ToArray();
        }

        public async Task<byte[]> GenerateClassGradeReportAsync(int classId, string semester, string academicYear)
        {
            var classInfo = await _context.Classes
                .FirstOrDefaultAsync(c => c.Id == classId);

            if (classInfo == null)
                throw new ArgumentException("Class not found");

            var students = await _context.Students
                .Include(s => s.User)
                .Include(s => s.Grades.Where(g => g.Semester == semester && g.AcademicYear == academicYear))
                .ThenInclude(g => g.Subject)
                .Where(s => s.ClassId == classId && s.Status == StudentStatus.Active)
                .ToListAsync();

            using var stream = new MemoryStream();
            var document = new Document(PageSize.A4.Rotate()); // Landscape for wider table
            var writer = PdfWriter.GetInstance(document, stream);

            document.Open();

            var schoolName = _configuration["SchoolSettings:SchoolName"] ?? "School Management System";
            var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 16);
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 10);
            var normalFont = FontFactory.GetFont(FontFactory.HELVETICA, 8);

            document.Add(new Paragraph(schoolName, titleFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(new Paragraph($"Class Grade Report - {classInfo.FullName}", headerFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(new Paragraph($"Academic Year: {academicYear} - Semester: {semester}", normalFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(new Paragraph(" "));

            // Get all subjects for this class
            var subjects = await _context.ClassSubjects
                .Include(cs => cs.Subject)
                .Where(cs => cs.ClassId == classId && cs.IsActive)
                .Select(cs => cs.Subject)
                .ToListAsync();

            // Create table with dynamic columns
            var columnCount = subjects.Count + 3; // Student info + subjects + GPA
            var table = new PdfPTable(columnCount) { WidthPercentage = 100 };

            // Headers
            table.AddCell(new PdfPCell(new Phrase("Student", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
            table.AddCell(new PdfPCell(new Phrase("Number", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });

            foreach (var subject in subjects)
            {
                table.AddCell(new PdfPCell(new Phrase(subject.Name, headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
            }

            table.AddCell(new PdfPCell(new Phrase("GPA", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });

            // Student data
            foreach (var student in students.OrderBy(s => s.User.FirstName))
            {
                table.AddCell(new Phrase(student.User.FullName, normalFont));
                table.AddCell(new Phrase(student.StudentNumber, normalFont));

                decimal totalGPA = 0;
                int gradeCount = 0;

                foreach (var subject in subjects)
                {
                    var grade = student.Grades.FirstOrDefault(g => g.SubjectId == subject.Id);
                    if (grade != null)
                    {
                        table.AddCell(new Phrase($"{grade.TotalScore:F1}", normalFont));
                        totalGPA += (decimal)grade.GPA;
                        gradeCount++;
                    }
                    else
                    {
                        table.AddCell(new Phrase("-", normalFont));
                    }
                }

                var studentGPA = gradeCount > 0 ? totalGPA / gradeCount : 0;
                table.AddCell(new Phrase($"{studentGPA:F2}", normalFont));
            }

            document.Add(table);
            document.Add(new Paragraph(" "));
            document.Add(new Paragraph($"Generated on: {DateTime.Now:yyyy/MM/dd HH:mm}", normalFont));

            document.Close();
            return stream.ToArray();
        }

        public async Task<byte[]> GenerateAttendanceReportAsync(int studentId, DateTime startDate, DateTime endDate)
        {
            var student = await _context.Students
                .Include(s => s.User)
                .Include(s => s.Class)
                .FirstOrDefaultAsync(s => s.Id == studentId);

            if (student == null)
                throw new ArgumentException("Student not found");

            var attendances = await _context.Attendances
                .Include(a => a.Subject)
                .Where(a => a.StudentId == studentId && a.Date >= startDate && a.Date <= endDate)
                .OrderBy(a => a.Date)
                .ToListAsync();

            using var stream = new MemoryStream();
            var document = new Document(PageSize.A4);
            var writer = PdfWriter.GetInstance(document, stream);

            document.Open();

            var schoolName = _configuration["SchoolSettings:SchoolName"] ?? "School Management System";
            var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 16);
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);
            var normalFont = FontFactory.GetFont(FontFactory.HELVETICA, 10);

            document.Add(new Paragraph(schoolName, titleFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(new Paragraph("Attendance Report", headerFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(new Paragraph($"Period: {startDate:yyyy/MM/dd} - {endDate:yyyy/MM/dd}", normalFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(new Paragraph(" "));

            // Student information
            document.Add(new Paragraph($"Student: {student.User.FullName}", normalFont));
            document.Add(new Paragraph($"Student Number: {student.StudentNumber}", normalFont));
            document.Add(new Paragraph($"Class: {student.Class.FullName}", normalFont));
            document.Add(new Paragraph(" "));

            // Attendance summary
            var totalDays = attendances.Count;
            var presentDays = attendances.Count(a => a.Status == AttendanceStatus.Present);
            var absentDays = attendances.Count(a => a.Status == AttendanceStatus.Absent);
            var lateDays = attendances.Count(a => a.Status == AttendanceStatus.Late);
            var attendancePercentage = totalDays > 0 ? (double)presentDays / totalDays * 100 : 0;

            document.Add(new Paragraph("Attendance Summary:", headerFont));
            document.Add(new Paragraph($"Total Days: {totalDays}", normalFont));
            document.Add(new Paragraph($"Present: {presentDays}", normalFont));
            document.Add(new Paragraph($"Absent: {absentDays}", normalFont));
            document.Add(new Paragraph($"Late: {lateDays}", normalFont));
            document.Add(new Paragraph($"Attendance Percentage: {attendancePercentage:F1}%", normalFont));
            document.Add(new Paragraph(" "));

            // Detailed attendance table
            var table = new PdfPTable(4) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 2, 2, 2, 3 });

            table.AddCell(new PdfPCell(new Phrase("Date", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
            table.AddCell(new PdfPCell(new Phrase("Subject", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
            table.AddCell(new PdfPCell(new Phrase("Status", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
            table.AddCell(new PdfPCell(new Phrase("Notes", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });

            foreach (var attendance in attendances)
            {
                table.AddCell(new Phrase(attendance.Date.ToString("yyyy/MM/dd"), normalFont));
                table.AddCell(new Phrase(attendance.Subject?.Name ?? "General", normalFont));
                table.AddCell(new Phrase(attendance.Status.ToString(), normalFont));
                table.AddCell(new Phrase(attendance.Notes ?? "", normalFont));
            }

            document.Add(table);
            document.Add(new Paragraph(" "));
            document.Add(new Paragraph($"Generated on: {DateTime.Now:yyyy/MM/dd HH:mm}", normalFont));

            document.Close();
            return stream.ToArray();
        }

        // Implement other report methods similarly...
        public Task<byte[]> GenerateClassAttendanceReportAsync(int classId, DateTime startDate, DateTime endDate)
        {
            // Implementation similar to student attendance but for entire class
            throw new NotImplementedException();
        }

        public Task<byte[]> GenerateFinancialReportAsync(DateTime startDate, DateTime endDate)
        {
            // Implementation for financial reports
            throw new NotImplementedException();
        }

        public Task<byte[]> GenerateTeacherScheduleAsync(int teacherId, string semester, string academicYear)
        {
            // Implementation for teacher schedule
            throw new NotImplementedException();
        }

        public Task<byte[]> GenerateClassScheduleAsync(int classId, string semester, string academicYear)
        {
            // Implementation for class schedule
            throw new NotImplementedException();
        }

        public Task<byte[]> GenerateStudentListAsync(int classId)
        {
            // Implementation for student list
            throw new NotImplementedException();
        }

        public Task<byte[]> GeneratePaymentReportAsync(int studentId, string academicYear)
        {
            // Implementation for payment report
            throw new NotImplementedException();
        }

        public Task<byte[]> GenerateOverduePaymentsReportAsync()
        {
            // Implementation for overdue payments report
            throw new NotImplementedException();
        }
    }
}
