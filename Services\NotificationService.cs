using Microsoft.EntityFrameworkCore;
using SchoolManager.Data;
using System.Text;
using System.Text.Json;

namespace SchoolManager.Services
{
    public class SmsSettings
    {
        public string ApiKey { get; set; } = string.Empty;
        public string ApiUrl { get; set; } = string.Empty;
        public string SenderName { get; set; } = string.Empty;
    }

    public class NotificationService : INotificationService
    {
        private readonly SchoolContext _context;
        private readonly IEmailService _emailService;
        private readonly SmsSettings _smsSettings;
        private readonly ILogger<NotificationService> _logger;
        private readonly HttpClient _httpClient;

        public NotificationService(
            SchoolContext context,
            IEmailService emailService,
            IConfiguration configuration,
            ILogger<NotificationService> logger,
            HttpClient httpClient)
        {
            _context = context;
            _emailService = emailService;
            _smsSettings = configuration.GetSection("SmsSettings").Get<SmsSettings>() ?? new SmsSettings();
            _logger = logger;
            _httpClient = httpClient;
        }

        public async Task SendSmsAsync(string phoneNumber, string message)
        {
            try
            {
                if (string.IsNullOrEmpty(_smsSettings.ApiKey) || string.IsNullOrEmpty(_smsSettings.ApiUrl))
                {
                    _logger.LogWarning("SMS settings not configured. SMS not sent.");
                    return;
                }

                var payload = new
                {
                    api_key = _smsSettings.ApiKey,
                    sender = _smsSettings.SenderName,
                    to = phoneNumber,
                    message = message
                };

                var json = JsonSerializer.Serialize(payload);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_smsSettings.ApiUrl, content);
                
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation($"SMS sent successfully to {phoneNumber}");
                }
                else
                {
                    _logger.LogError($"Failed to send SMS to {phoneNumber}. Status: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending SMS to {phoneNumber}");
            }
        }

        public async Task SendBulkSmsAsync(List<string> phoneNumbers, string message)
        {
            var tasks = phoneNumbers.Select(phone => SendSmsAsync(phone, message));
            await Task.WhenAll(tasks);
        }

        public async Task NotifyParentAboutAbsenceAsync(int studentId, DateTime date)
        {
            var student = await _context.Students
                .Include(s => s.User)
                .FirstOrDefaultAsync(s => s.Id == studentId);

            if (student == null) return;

            var message = $"عزيزي ولي الأمر، نود إعلامكم بأن الطالب/ة {student.User.FullName} غائب/ة اليوم {date:yyyy/MM/dd}. للاستفسار اتصل بالمدرسة.";
            
            // Send SMS
            if (!string.IsNullOrEmpty(student.GuardianPhone))
            {
                await SendSmsAsync(student.GuardianPhone, message);
            }

            // Send Email
            if (!string.IsNullOrEmpty(student.GuardianEmail))
            {
                await _emailService.SendAttendanceNotificationAsync(
                    student.GuardianEmail,
                    student.User.FullName,
                    $"نود إعلامكم بأن الطالب/ة غائب/ة اليوم {date:yyyy/MM/dd}"
                );
            }
        }

        public async Task NotifyParentAboutGradesAsync(int studentId, string semester)
        {
            var student = await _context.Students
                .Include(s => s.User)
                .Include(s => s.Grades.Where(g => g.Semester == semester))
                .ThenInclude(g => g.Subject)
                .FirstOrDefaultAsync(s => s.Id == studentId);

            if (student == null || !student.Grades.Any()) return;

            var gradesSummary = new StringBuilder();
            gradesSummary.AppendLine("ملخص الدرجات:");
            
            foreach (var grade in student.Grades)
            {
                gradesSummary.AppendLine($"- {grade.Subject.Name}: {grade.TotalScore:F1} ({grade.LetterGrade})");
            }

            var averageGPA = student.Grades.Average(g => g.GPA);
            gradesSummary.AppendLine($"المعدل العام: {averageGPA:F2}");

            // Send SMS (shorter version)
            if (!string.IsNullOrEmpty(student.GuardianPhone))
            {
                var smsMessage = $"تم رفع درجات الطالب/ة {student.User.FullName} للفصل {semester}. المعدل: {averageGPA:F2}. تفاصيل أكثر عبر البريد الإلكتروني.";
                await SendSmsAsync(student.GuardianPhone, smsMessage);
            }

            // Send detailed email
            if (!string.IsNullOrEmpty(student.GuardianEmail))
            {
                await _emailService.SendGradeReportAsync(
                    student.GuardianEmail,
                    student.User.FullName,
                    gradesSummary.ToString()
                );
            }
        }

        public async Task NotifyAboutPaymentDueAsync(int studentId, decimal amount, DateTime dueDate)
        {
            var student = await _context.Students
                .Include(s => s.User)
                .FirstOrDefaultAsync(s => s.Id == studentId);

            if (student == null) return;

            var message = $"عزيزي ولي الأمر، يوجد مبلغ مستحق للطالب/ة {student.User.FullName}: {amount:C} ريال. تاريخ الاستحقاق: {dueDate:yyyy/MM/dd}";

            // Send SMS
            if (!string.IsNullOrEmpty(student.GuardianPhone))
            {
                await SendSmsAsync(student.GuardianPhone, message);
            }

            // Send Email
            if (!string.IsNullOrEmpty(student.GuardianEmail))
            {
                await _emailService.SendPaymentReminderAsync(
                    student.GuardianEmail,
                    student.User.FullName,
                    amount,
                    dueDate
                );
            }
        }

        public async Task NotifyTeacherAboutScheduleChangeAsync(int teacherId, string message)
        {
            var teacher = await _context.Teachers
                .Include(t => t.User)
                .FirstOrDefaultAsync(t => t.Id == teacherId);

            if (teacher == null) return;

            var fullMessage = $"أستاذ/ة {teacher.User.FullName}، {message}";

            // Send SMS
            if (!string.IsNullOrEmpty(teacher.User.PhoneNumber))
            {
                await SendSmsAsync(teacher.User.PhoneNumber, fullMessage);
            }

            // Send Email
            if (!string.IsNullOrEmpty(teacher.User.Email))
            {
                await _emailService.SendEmailAsync(
                    teacher.User.Email,
                    "تغيير في الجدول الدراسي",
                    $"<div dir='rtl'><p>{fullMessage}</p><p>مع تحيات إدارة المدرسة</p></div>"
                );
            }
        }

        public async Task SendEmergencyNotificationAsync(string message, List<string> recipients)
        {
            var emergencyMessage = $"إشعار طارئ: {message}";

            // Send to all recipients via SMS and Email
            var emailTasks = recipients.Where(r => r.Contains("@"))
                .Select(email => _emailService.SendEmailAsync(
                    email,
                    "إشعار طارئ - Emergency Notification",
                    $"<div dir='rtl' style='color: red; font-weight: bold;'><p>{emergencyMessage}</p></div>"
                ));

            var smsTasks = recipients.Where(r => !r.Contains("@"))
                .Select(phone => SendSmsAsync(phone, emergencyMessage));

            await Task.WhenAll(emailTasks.Concat(smsTasks));
        }
    }
}
