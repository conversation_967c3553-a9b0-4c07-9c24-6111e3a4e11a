@using Microsoft.AspNetCore.Identity
@inject SignInManager<SchoolManager.Models.ApplicationUser> SignInManager
@inject UserManager<SchoolManager.Models.ApplicationUser> UserManager

<ul class="navbar-nav">
@if (SignInManager.IsSignedIn(User))
{
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-user me-1"></i>
            مرحباً، @User.Identity?.Name
        </a>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
            <li>
                <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index" title="Manage">
                    <i class="fas fa-user-cog me-2"></i>الملف الشخصي
                </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <form class="form-inline" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                    <button type="submit" class="dropdown-item">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </button>
                </form>
            </li>
        </ul>
    </li>
}
else
{
    <li class="nav-item">
        <a class="nav-link text-dark" asp-area="Identity" asp-page="/Account/Register">
            <i class="fas fa-user-plus me-1"></i>تسجيل جديد
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link text-dark" asp-area="Identity" asp-page="/Account/Login">
            <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
        </a>
    </li>
}
</ul>
