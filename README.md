# 🎓 نظام إدارة المدرسة - School Management System

نظام متكامل لإدارة المدارس مبني بتقنية ASP.NET Core مع دعم كامل للغة العربية وواجهة مستخدم متجاوبة.

## 🌟 المميزات الرئيسية

### 👨‍🎓 إدارة الطلاب
- ✅ تسجيل وإدارة بيانات الطلاب الشخصية
- ✅ تتبع الحضور والغياب اليومي
- ✅ إدارة الدرجات والتقييمات
- ✅ سجل السلوك والانضباط
- ✅ معلومات أولياء الأمور والتواصل

### 👩‍🏫 إدارة المعلمين
- ✅ ملفات المعلمين الشخصية والمهنية
- ✅ تخصيص المواد والصفوف
- ✅ جدولة الحصص الدراسية
- ✅ تقييم الأداء والتطوير المهني

### 📚 إدارة المناهج والمواد
- ✅ إنشاء وتنظيم المواد الدراسية
- ✅ ربط المواد بالصفوف والمعلمين
- ✅ توزيع الدرجات والأوزان
- ✅ متطلبات المواد المسبقة

### 📅 الجدول الدراسي
- ✅ إنشاء الجداول الأسبوعية
- ✅ تجنب التعارضات التلقائي
- ✅ طباعة وتصدير الجداول
- ✅ إشعارات التغييرات

### 📊 الدرجات والتقييم
- ✅ نظام درجات مرن ومتعدد المكونات
- ✅ حساب المعدلات التلقائي
- ✅ كشوف الدرجات الإلكترونية
- ✅ تقارير الأداء الأكاديمي

### 💰 الإدارة المالية
- ✅ إدارة الرسوم الدراسية
- ✅ تتبع المدفوعات والمتأخرات
- ✅ نظام الخصومات والمنح
- ✅ التقارير المالية الشاملة

### 📧 الإشعارات والتواصل
- ✅ إرسال الإشعارات عبر البريد الإلكتروني
- ✅ رسائل SMS للحالات الطارئة
- ✅ تنبيهات الغياب والدرجات
- ✅ تذكيرات المدفوعات

### 🔐 الأمان والصلاحيات
- ✅ نظام مستخدمين متعدد المستويات
- ✅ صلاحيات مخصصة لكل دور
- ✅ تشفير البيانات الحساسة
- ✅ سجل أنشطة المستخدمين

### 📈 التقارير والإحصائيات
- ✅ تقارير PDF قابلة للطباعة
- ✅ إحصائيات الحضور والأداء
- ✅ التقارير المالية المفصلة
- ✅ تحليلات البيانات التفاعلية

## 🛠️ التقنيات المستخدمة

- **Backend**: ASP.NET Core 8.0
- **Database**: Entity Framework Core مع SQLite/SQL Server
- **Authentication**: ASP.NET Core Identity
- **Frontend**: Bootstrap 5 مع دعم RTL
- **PDF Generation**: iTextSharp
- **Email**: MailKit
- **Logging**: Serilog
- **Validation**: FluentValidation

## 📋 متطلبات النظام

- .NET 8.0 SDK أو أحدث
- Visual Studio 2022 أو VS Code
- SQL Server أو SQLite (افتراضي)

## 🚀 التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-repo/school-manager.git
cd school-manager
```

### 2. استعادة الحزم
```bash
dotnet restore
```

### 3. إعداد قاعدة البيانات
```bash
dotnet ef database update
```

### 4. تشغيل التطبيق
```bash
dotnet run
```

### 5. الوصول للتطبيق
افتح المتصفح وانتقل إلى: `https://localhost:5001`

## 👤 حسابات المستخدمين الافتراضية

### مدير النظام
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Admin123!

### معلم تجريبي
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Teacher123!

### طالب تجريبي
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Student123!

## ⚙️ الإعدادات

### إعدادات البريد الإلكتروني
قم بتحديث `appsettings.json`:
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "your-app-password",
    "SenderName": "School Management System"
  }
}
```

### إعدادات المدرسة
```json
{
  "SchoolSettings": {
    "SchoolName": "مدرسة المستقبل الثانوية",
    "SchoolAddress": "الرياض، المملكة العربية السعودية",
    "SchoolPhone": "+966-11-1234567",
    "SchoolEmail": "<EMAIL>",
    "AcademicYear": "2024-2025"
  }
}
```

## 📱 الواجهات المدعومة

- **لوحة تحكم المدير**: إدارة شاملة للنظام
- **واجهة المعلم**: إدارة الصفوف والدرجات
- **واجهة الطالب**: عرض الدرجات والجدول
- **واجهة ولي الأمر**: متابعة أداء الطالب

## 🔧 التطوير والمساهمة

### هيكل المشروع
```
SchoolManager/
├── Controllers/          # وحدات التحكم
├── Models/              # نماذج البيانات
├── Views/               # واجهات المستخدم
├── Data/                # سياق قاعدة البيانات
├── Services/            # الخدمات المساعدة
├── wwwroot/             # الملفات الثابتة
└── appsettings.json     # إعدادات التطبيق
```

### إضافة ميزة جديدة
1. إنشاء النموذج في `Models/`
2. تحديث `SchoolContext.cs`
3. إنشاء Migration جديد
4. إضافة Controller
5. إنشاء Views المطلوبة

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **AspNetUsers**: المستخدمين
- **Students**: الطلاب
- **Teachers**: المعلمين
- **Classes**: الصفوف
- **Subjects**: المواد
- **Grades**: الدرجات
- **Attendance**: الحضور
- **Payments**: المدفوعات
- **Schedules**: الجداول

## 🔒 الأمان

- تشفير كلمات المرور باستخدام ASP.NET Core Identity
- حماية من CSRF و XSS
- تسجيل جميع العمليات الحساسة
- صلاحيات مبنية على الأدوار

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق التفصيلية

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام والمكتبات مفتوحة المصدر المستخدمة.

---

**تم تطويره بـ ❤️ لخدمة التعليم في الوطن العربي**
