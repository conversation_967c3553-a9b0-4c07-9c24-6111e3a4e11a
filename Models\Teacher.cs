using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SchoolManager.Models
{
    public class Teacher
    {
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required]
        public string UserId { get; set; } = string.Empty;
        public ApplicationUser User { get; set; } = null!;

        [Required]
        [StringLength(100)]
        public string Qualification { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Specialization { get; set; }

        public DateTime HireDate { get; set; } = DateTime.UtcNow;

        [Column(TypeName = "decimal(10,2)")]
        public decimal Salary { get; set; }

        public TeacherStatus Status { get; set; } = TeacherStatus.Active;

        [StringLength(500)]
        public string? Notes { get; set; }

        public int YearsOfExperience { get; set; }

        [StringLength(200)]
        public string? PreviousSchools { get; set; }

        [StringLength(100)]
        public string? EmergencyContact { get; set; }

        [StringLength(15)]
        public string? EmergencyPhone { get; set; }

        // Navigation properties
        public ICollection<SubjectTeacher> SubjectTeachers { get; set; } = new List<SubjectTeacher>();
        public ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();
        public ICollection<Grade> Grades { get; set; } = new List<Grade>();
        public ICollection<TeacherEvaluation> Evaluations { get; set; } = new List<TeacherEvaluation>();
    }

    public enum TeacherStatus
    {
        Active = 1,
        Inactive = 2,
        OnLeave = 3,
        Terminated = 4
    }

    public class SubjectTeacher
    {
        public int Id { get; set; }
        public int TeacherId { get; set; }
        public Teacher Teacher { get; set; } = null!;
        public int SubjectId { get; set; }
        public Subject Subject { get; set; } = null!;
        public int ClassId { get; set; }
        public Class Class { get; set; } = null!;
        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
        public bool IsActive { get; set; } = true;
    }

    public class TeacherEvaluation
    {
        public int Id { get; set; }
        public int TeacherId { get; set; }
        public Teacher Teacher { get; set; } = null!;
        
        [StringLength(100)]
        public string EvaluatedBy { get; set; } = string.Empty;
        
        public DateTime EvaluationDate { get; set; } = DateTime.UtcNow;
        
        public int TeachingSkillsScore { get; set; } // 1-10
        public int ClassManagementScore { get; set; } // 1-10
        public int CommunicationScore { get; set; } // 1-10
        public int ProfessionalismScore { get; set; } // 1-10
        
        [StringLength(1000)]
        public string? Comments { get; set; }
        
        [StringLength(1000)]
        public string? Recommendations { get; set; }
        
        public double OverallScore => (TeachingSkillsScore + ClassManagementScore + CommunicationScore + ProfessionalismScore) / 4.0;
    }
}
