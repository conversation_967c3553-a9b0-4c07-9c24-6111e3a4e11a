using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SchoolManager.Models
{
    public class Grade
    {
        public int Id { get; set; }

        public int StudentId { get; set; }
        public Student Student { get; set; } = null!;

        public int SubjectId { get; set; }
        public Subject Subject { get; set; } = null!;

        public int TeacherId { get; set; }
        public Teacher Teacher { get; set; } = null!;

        [StringLength(20)]
        public string Semester { get; set; } = string.Empty; // الفصل الأول، الثاني

        [StringLength(50)]
        public string AcademicYear { get; set; } = string.Empty;

        // Grade components
        [Column(TypeName = "decimal(5,2)")]
        public decimal? AttendanceScore { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? AssignmentScore { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? MidtermScore { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? FinalScore { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? BonusScore { get; set; }

        public DateTime? DateRecorded { get; set; }

        public DateTime LastModified { get; set; } = DateTime.UtcNow;

        [StringLength(100)]
        public string? ModifiedBy { get; set; }

        [StringLength(500)]
        public string? Comments { get; set; }

        public GradeStatus Status { get; set; } = GradeStatus.InProgress;

        // Calculated properties
        [Column(TypeName = "decimal(5,2)")]
        public decimal TotalScore
        {
            get
            {
                decimal total = 0;
                if (AttendanceScore.HasValue) total += AttendanceScore.Value * (Subject?.AttendanceWeight ?? 10) / 100;
                if (AssignmentScore.HasValue) total += AssignmentScore.Value * (Subject?.AssignmentWeight ?? 20) / 100;
                if (MidtermScore.HasValue) total += MidtermScore.Value * (Subject?.MidtermWeight ?? 30) / 100;
                if (FinalScore.HasValue) total += FinalScore.Value * (Subject?.FinalWeight ?? 40) / 100;
                if (BonusScore.HasValue) total += BonusScore.Value;
                
                return Math.Min(total, 100); // Cap at 100
            }
        }

        public string LetterGrade
        {
            get
            {
                var score = TotalScore;
                return score switch
                {
                    >= 90 => "A+",
                    >= 85 => "A",
                    >= 80 => "B+",
                    >= 75 => "B",
                    >= 70 => "C+",
                    >= 65 => "C",
                    >= 60 => "D+",
                    >= 50 => "D",
                    _ => "F"
                };
            }
        }

        public double GPA
        {
            get
            {
                var score = TotalScore;
                return score switch
                {
                    >= 90 => 4.0,
                    >= 85 => 3.7,
                    >= 80 => 3.3,
                    >= 75 => 3.0,
                    >= 70 => 2.7,
                    >= 65 => 2.3,
                    >= 60 => 2.0,
                    >= 50 => 1.0,
                    _ => 0.0
                };
            }
        }

        public bool IsPassing => TotalScore >= 50;
    }

    public enum GradeStatus
    {
        InProgress = 1,
        Completed = 2,
        Incomplete = 3,
        Withdrawn = 4
    }
}
