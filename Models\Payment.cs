using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SchoolManager.Models
{
    public class Payment
    {
        public int Id { get; set; }

        public int StudentId { get; set; }
        public Student Student { get; set; } = null!;

        [Required]
        [StringLength(100)]
        public string Description { get; set; } = string.Empty;

        [Column(TypeName = "decimal(10,2)")]
        public decimal Amount { get; set; }

        public PaymentType Type { get; set; }

        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        public DateTime DueDate { get; set; }

        public DateTime? PaidDate { get; set; }

        [StringLength(50)]
        public string? PaymentMethod { get; set; }

        [StringLength(100)]
        public string? TransactionReference { get; set; }

        [StringLength(100)]
        public string? ReceivedBy { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(20)]
        public string Semester { get; set; } = string.Empty;

        [StringLength(50)]
        public string AcademicYear { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column(TypeName = "decimal(10,2)")]
        public decimal? DiscountAmount { get; set; }

        [StringLength(200)]
        public string? DiscountReason { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal NetAmount => Amount - (DiscountAmount ?? 0);

        public bool IsOverdue => Status == PaymentStatus.Pending && DueDate < DateTime.Today;
        public int DaysOverdue => IsOverdue ? (DateTime.Today - DueDate).Days : 0;
    }

    public enum PaymentType
    {
        Tuition = 1,        // رسوم دراسية
        Registration = 2,   // رسوم تسجيل
        Books = 3,          // رسوم كتب
        Transportation = 4, // رسوم نقل
        Activities = 5,     // رسوم أنشطة
        Uniform = 6,        // رسوم زي مدرسي
        Laboratory = 7,     // رسوم مختبر
        Library = 8,        // رسوم مكتبة
        Other = 9           // أخرى
    }

    public enum PaymentStatus
    {
        Pending = 1,
        Paid = 2,
        Overdue = 3,
        Cancelled = 4,
        Refunded = 5,
        PartiallyPaid = 6
    }

    public class PaymentSummary
    {
        public int StudentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public decimal OverdueAmount { get; set; }
        public int OverdueCount { get; set; }
        public DateTime? LastPaymentDate { get; set; }
    }

    public class FinancialReport
    {
        public string Period { get; set; } = string.Empty;
        public decimal TotalRevenue { get; set; }
        public decimal TotalPending { get; set; }
        public decimal TotalOverdue { get; set; }
        public int TotalStudents { get; set; }
        public int StudentsWithOverdue { get; set; }
        public Dictionary<PaymentType, decimal> RevenueByType { get; set; } = new Dictionary<PaymentType, decimal>();
    }
}
