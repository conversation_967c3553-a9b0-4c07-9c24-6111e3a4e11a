{"ConnectionStrings": {"DefaultConnection": "Data Source=SchoolManager.db"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SenderEmail": "<EMAIL>", "SenderPassword": "your-app-password", "SenderName": "School Management System"}, "SchoolSettings": {"SchoolName": "مدرسة المستقبل الثانوية", "SchoolAddress": "الرياض، المملكة العربية السعودية", "SchoolPhone": "+966-11-1234567", "SchoolEmail": "<EMAIL>", "AcademicYear": "2024-2025"}, "SmsSettings": {"ApiKey": "your-sms-api-key", "ApiUrl": "https://api.sms-provider.com/send", "SenderName": "SchoolSMS"}}