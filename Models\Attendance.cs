using System.ComponentModel.DataAnnotations;

namespace SchoolManager.Models
{
    public class Attendance
    {
        public int Id { get; set; }

        public int StudentId { get; set; }
        public Student Student { get; set; } = null!;

        public int? SubjectId { get; set; }
        public Subject? Subject { get; set; }

        public int? TeacherId { get; set; }
        public Teacher? Teacher { get; set; }

        public DateTime Date { get; set; }

        public AttendanceStatus Status { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public DateTime RecordedAt { get; set; } = DateTime.UtcNow;

        [StringLength(100)]
        public string? RecordedBy { get; set; }

        public TimeSpan? CheckInTime { get; set; }
        public TimeSpan? CheckOutTime { get; set; }

        [StringLength(100)]
        public string? ExcuseReason { get; set; }

        public bool IsExcused { get; set; } = false;
    }

    public enum AttendanceStatus
    {
        Present = 1,
        Absent = 2,
        Late = 3,
        Excused = 4,
        Sick = 5,
        Emergency = 6
    }

    public class AttendanceSummary
    {
        public int StudentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public int TotalDays { get; set; }
        public int PresentDays { get; set; }
        public int AbsentDays { get; set; }
        public int LateDays { get; set; }
        public int ExcusedDays { get; set; }
        public double AttendancePercentage => TotalDays > 0 ? (double)PresentDays / TotalDays * 100 : 0;
    }
}
