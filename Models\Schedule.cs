using System.ComponentModel.DataAnnotations;

namespace SchoolManager.Models
{
    public class Schedule
    {
        public int Id { get; set; }

        public int ClassId { get; set; }
        public Class Class { get; set; } = null!;

        public int SubjectId { get; set; }
        public Subject Subject { get; set; } = null!;

        public int TeacherId { get; set; }
        public Teacher Teacher { get; set; } = null!;

        public DayOfWeek DayOfWeek { get; set; }

        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }

        [StringLength(50)]
        public string? Classroom { get; set; }

        public int Period { get; set; } // رقم الحصة

        [StringLength(20)]
        public string Semester { get; set; } = string.Empty;

        [StringLength(50)]
        public string AcademicYear { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(500)]
        public string? Notes { get; set; }

        public string TimeSlot => $"{StartTime:hh\\:mm} - {EndTime:hh\\:mm}";
        public string DayName => DayOfWeek.ToString();
    }

    public class ScheduleConflict
    {
        public int TeacherId { get; set; }
        public string TeacherName { get; set; } = string.Empty;
        public DayOfWeek Day { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public List<string> ConflictingClasses { get; set; } = new List<string>();
    }

    public class WeeklySchedule
    {
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public Dictionary<DayOfWeek, List<SchedulePeriod>> Schedule { get; set; } = new Dictionary<DayOfWeek, List<SchedulePeriod>>();
    }

    public class SchedulePeriod
    {
        public int Period { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string TeacherName { get; set; } = string.Empty;
        public string? Classroom { get; set; }
        public string TimeSlot => $"{StartTime:hh\\:mm} - {EndTime:hh\\:mm}";
    }
}
