<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام إدارة المدرسة - عرض تجريبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            text-align: center;
            transition: transform 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            text-align: center;
            transition: transform 0.3s;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 50px;
            font-size: 1rem;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 500px;
            padding: 20px;
        }
        .nav-link {
            color: white;
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s;
            text-decoration: none;
            display: block;
        }
        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        .dashboard-content {
            padding: 30px;
        }
        .alert-demo {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="main-container">
            <div class="header">
                <i class="fas fa-graduation-cap fa-4x mb-3"></i>
                <h1 class="display-4 mb-3">نظام إدارة المدرسة</h1>
                <p class="lead mb-0">عرض تجريبي للنظام المتكامل</p>
                <div class="mt-3">
                    <span class="badge bg-light text-dark fs-6 me-2">ASP.NET Core</span>
                    <span class="badge bg-light text-dark fs-6 me-2">Entity Framework</span>
                    <span class="badge bg-light text-dark fs-6">Bootstrap 5</span>
                </div>
            </div>

            <!-- Dashboard Demo -->
            <div class="row g-0">
                <div class="col-md-3">
                    <div class="sidebar">
                        <h5 class="text-white mb-4">
                            <i class="fas fa-user-shield me-2"></i>
                            لوحة المدير
                        </h5>
                        <nav>
                            <a href="#" class="nav-link active">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                            </a>
                            <a href="#" class="nav-link">
                                <i class="fas fa-user-graduate me-2"></i>الطلاب
                            </a>
                            <a href="#" class="nav-link">
                                <i class="fas fa-chalkboard-teacher me-2"></i>المعلمين
                            </a>
                            <a href="#" class="nav-link">
                                <i class="fas fa-school me-2"></i>الصفوف
                            </a>
                            <a href="#" class="nav-link">
                                <i class="fas fa-book me-2"></i>المواد
                            </a>
                            <a href="#" class="nav-link">
                                <i class="fas fa-chart-line me-2"></i>الدرجات
                            </a>
                            <a href="#" class="nav-link">
                                <i class="fas fa-calendar-check me-2"></i>الحضور
                            </a>
                            <a href="#" class="nav-link">
                                <i class="fas fa-money-bill-wave me-2"></i>المالية
                            </a>
                            <a href="#" class="nav-link">
                                <i class="fas fa-file-alt me-2"></i>التقارير
                            </a>
                        </nav>
                    </div>
                </div>
                
                <div class="col-md-9">
                    <div class="dashboard-content">
                        <!-- Welcome Alert -->
                        <div class="alert alert-info alert-demo mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>مرحباً بك في النظام!</strong> هذا عرض تجريبي لنظام إدارة المدرسة المتكامل
                        </div>

                        <!-- Stats Cards -->
                        <div class="row">
                            <div class="col-xl-3 col-md-6">
                                <div class="stats-card">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div>
                                            <h3>450</h3>
                                            <p class="mb-0">إجمالي الطلاب</p>
                                        </div>
                                        <i class="fas fa-user-graduate fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6">
                                <div class="stats-card">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div>
                                            <h3>25</h3>
                                            <p class="mb-0">إجمالي المعلمين</p>
                                        </div>
                                        <i class="fas fa-chalkboard-teacher fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6">
                                <div class="stats-card">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div>
                                            <h3>12</h3>
                                            <p class="mb-0">إجمالي الصفوف</p>
                                        </div>
                                        <i class="fas fa-school fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6">
                                <div class="stats-card">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div>
                                            <h3>18</h3>
                                            <p class="mb-0">إجمالي المواد</p>
                                        </div>
                                        <i class="fas fa-book fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Features Demo -->
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <i class="fas fa-user-graduate feature-icon"></i>
                                    <h5>إدارة الطلاب</h5>
                                    <p class="text-muted">تسجيل ومتابعة بيانات الطلاب، الدرجات، والحضور</p>
                                    <a href="#" class="btn-custom">عرض التفاصيل</a>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <i class="fas fa-chart-line feature-icon"></i>
                                    <h5>نظام الدرجات</h5>
                                    <p class="text-muted">إدارة متقدمة للدرجات مع حساب المعدلات التلقائي</p>
                                    <a href="#" class="btn-custom">عرض التفاصيل</a>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <i class="fas fa-file-alt feature-icon"></i>
                                    <h5>التقارير المتقدمة</h5>
                                    <p class="text-muted">تقارير PDF شاملة وإحصائيات مفصلة</p>
                                    <a href="#" class="btn-custom">عرض التفاصيل</a>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activities -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                        <h5 class="mb-0">
                                            <i class="fas fa-history me-2"></i>
                                            الأنشطة الأخيرة
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled mb-0">
                                            <li class="mb-3 d-flex align-items-center">
                                                <i class="fas fa-user-plus text-success me-3"></i>
                                                <div>
                                                    <strong>تم تسجيل طالب جديد:</strong> أحمد محمد علي - الصف الثالث أ
                                                    <small class="text-muted d-block">منذ 5 دقائق</small>
                                                </div>
                                            </li>
                                            <li class="mb-3 d-flex align-items-center">
                                                <i class="fas fa-chart-line text-primary me-3"></i>
                                                <div>
                                                    <strong>تم رفع الدرجات:</strong> مادة الرياضيات - الصف الثاني ب
                                                    <small class="text-muted d-block">منذ 15 دقيقة</small>
                                                </div>
                                            </li>
                                            <li class="mb-3 d-flex align-items-center">
                                                <i class="fas fa-money-bill-wave text-warning me-3"></i>
                                                <div>
                                                    <strong>تم استلام دفعة:</strong> 2,500 ريال من ولي أمر فاطمة أحمد
                                                    <small class="text-muted d-block">منذ 30 دقيقة</small>
                                                </div>
                                            </li>
                                            <li class="mb-0 d-flex align-items-center">
                                                <i class="fas fa-file-pdf text-danger me-3"></i>
                                                <div>
                                                    <strong>تم إنشاء تقرير:</strong> تقرير الحضور الشهري - يناير 2025
                                                    <small class="text-muted d-block">منذ ساعة</small>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Installation Instructions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-warning alert-demo">
                                    <h5><i class="fas fa-download me-2"></i>لتشغيل النظام الكامل:</h5>
                                    <ol class="mb-0">
                                        <li>قم بتثبيت .NET 8.0 SDK من <a href="https://dotnet.microsoft.com/download" target="_blank">هنا</a></li>
                                        <li>افتح Terminal في مجلد المشروع</li>
                                        <li>نفذ الأوامر التالية:
                                            <pre class="mt-2 p-2 bg-dark text-light rounded">dotnet restore
dotnet ef migrations add InitialCreate
dotnet ef database update
dotnet run</pre>
                                        </li>
                                        <li>افتح المتصفح على: <code>https://localhost:5001</code></li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add some interactivity
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-5px)';
                }, 200);
            });
        });

        // Simulate real-time updates
        setInterval(() => {
            const activities = document.querySelectorAll('.list-unstyled li small');
            activities.forEach((activity, index) => {
                const minutes = [5, 15, 30, 60];
                activity.textContent = `منذ ${minutes[index] + Math.floor(Math.random() * 5)} دقيقة`;
            });
        }, 30000);
    </script>
</body>
</html>
