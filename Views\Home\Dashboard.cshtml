@model SchoolManager.Controllers.DashboardViewModel
@{
    ViewData["Title"] = "لوحة التحكم";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <span class="badge bg-primary fs-6">@Model.UserRole</span>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-user me-2"></i>
            مرحباً <strong>@Model.UserName</strong>، أهلاً بك في نظام إدارة المدرسة
        </div>
    </div>
</div>

@if (Model.UserRole == "Admin")
{
    <!-- Admin Dashboard -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3>@Model.TotalStudents</h3>
                        <p class="mb-0">إجمالي الطلاب</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-graduate fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3>@Model.TotalTeachers</h3>
                        <p class="mb-0">إجمالي المعلمين</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-chalkboard-teacher fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3>@Model.TotalClasses</h3>
                        <p class="mb-0">إجمالي الصفوف</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-school fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3>@Model.TotalSubjects</h3>
                        <p class="mb-0">إجمالي المواد</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-book fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        الملخص المالي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h4 class="text-success">@Model.MonthlyRevenue.ToString("C") ريال</h4>
                            <p class="text-muted">إيرادات هذا الشهر</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">@Model.PendingPayments.ToString("C") ريال</h4>
                            <p class="text-muted">مدفوعات معلقة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        حضور اليوم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h4 class="text-success">@Model.TodayPresentCount</h4>
                            <p class="text-muted">حاضر</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger">@Model.TodayAbsentCount</h4>
                            <p class="text-muted">غائب</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (Model.RecentActivities.Any())
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            الأنشطة الأخيرة
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            @foreach (var activity in Model.RecentActivities)
                            {
                                <li class="mb-2">
                                    <i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>
                                    @activity
                                </li>
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
}
else if (Model.UserRole == "Teacher")
{
    <!-- Teacher Dashboard -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-school me-2"></i>
                        صفوفي
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.MyClasses.Any())
                    {
                        <ul class="list-unstyled mb-0">
                            @foreach (var className in Model.MyClasses)
                            {
                                <li class="mb-2">
                                    <i class="fas fa-users me-2 text-primary"></i>
                                    @className
                                </li>
                            }
                        </ul>
                    }
                    else
                    {
                        <p class="text-muted mb-0">لا توجد صفوف مخصصة</p>
                    }
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        جدول اليوم
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.TodaySchedule.Any())
                    {
                        <ul class="list-unstyled mb-0">
                            @foreach (var schedule in Model.TodaySchedule)
                            {
                                <li class="mb-2">
                                    <i class="fas fa-clock me-2 text-success"></i>
                                    @schedule
                                </li>
                            }
                        </ul>
                    }
                    else
                    {
                        <p class="text-muted mb-0">لا توجد حصص اليوم</p>
                    }
                </div>
            </div>
        </div>
    </div>

    @if (Model.PendingGrades > 0)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لديك <strong>@Model.PendingGrades</strong> درجة معلقة تحتاج إلى إدخال
                    <a href="@Url.Action("Index", "Grades")" class="btn btn-sm btn-warning ms-2">
                        عرض الدرجات المعلقة
                    </a>
                </div>
            </div>
        </div>
    }
}
else if (Model.UserRole == "Student")
{
    <!-- Student Dashboard -->
    <div class="row">
        <div class="col-lg-4">
            <div class="stats-card">
                <div class="text-center">
                    <h3>@Model.CurrentGPA.ToString("F2")</h3>
                    <p class="mb-0">المعدل التراكمي</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="stats-card">
                <div class="text-center">
                    <h3>@Model.AttendancePercentage.ToString("F1")%</h3>
                    <p class="mb-0">نسبة الحضور</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="stats-card">
                <div class="text-center">
                    <h3>@Model.PendingPaymentAmount.ToString("C")</h3>
                    <p class="mb-0">مدفوعات معلقة</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-school me-2"></i>
                        معلومات الصف
                    </h5>
                </div>
                <div class="card-body">
                    <h4>@Model.MyClass</h4>
                    <p class="text-muted mb-0">صفي الحالي</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        الدرجات الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.RecentGrades.Any())
                    {
                        <ul class="list-unstyled mb-0">
                            @foreach (var grade in Model.RecentGrades)
                            {
                                <li class="mb-2">
                                    <i class="fas fa-star me-2 text-warning"></i>
                                    @grade
                                </li>
                            }
                        </ul>
                    }
                    else
                    {
                        <p class="text-muted mb-0">لا توجد درجات محدثة</p>
                    }
                </div>
            </div>
        </div>
    </div>
}

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-link me-2"></i>
                    روابط سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @if (User.IsInRole("Admin"))
                    {
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Create", "Students")" class="btn btn-outline-primary w-100">
                                <i class="fas fa-user-plus d-block mb-2"></i>
                                إضافة طالب جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Index", "Reports")" class="btn btn-outline-success w-100">
                                <i class="fas fa-file-alt d-block mb-2"></i>
                                التقارير
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Index", "Payments")" class="btn btn-outline-warning w-100">
                                <i class="fas fa-money-bill-wave d-block mb-2"></i>
                                المالية
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Index", "Settings")" class="btn btn-outline-info w-100">
                                <i class="fas fa-cog d-block mb-2"></i>
                                الإعدادات
                            </a>
                        </div>
                    }
                    else if (User.IsInRole("Teacher"))
                    {
                        <div class="col-md-4 mb-3">
                            <a href="@Url.Action("Index", "Grades")" class="btn btn-outline-primary w-100">
                                <i class="fas fa-chart-line d-block mb-2"></i>
                                إدارة الدرجات
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="@Url.Action("Index", "Attendance")" class="btn btn-outline-success w-100">
                                <i class="fas fa-calendar-check d-block mb-2"></i>
                                تسجيل الحضور
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="@Url.Action("Index", "Schedule")" class="btn btn-outline-info w-100">
                                <i class="fas fa-calendar-alt d-block mb-2"></i>
                                الجدول الدراسي
                            </a>
                        </div>
                    }
                    else if (User.IsInRole("Student"))
                    {
                        <div class="col-md-4 mb-3">
                            <a href="@Url.Action("MyGrades", "Grades")" class="btn btn-outline-primary w-100">
                                <i class="fas fa-chart-line d-block mb-2"></i>
                                درجاتي
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="@Url.Action("MyAttendance", "Attendance")" class="btn btn-outline-success w-100">
                                <i class="fas fa-calendar-check d-block mb-2"></i>
                                حضوري
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="@Url.Action("MySchedule", "Schedule")" class="btn btn-outline-info w-100">
                                <i class="fas fa-calendar-alt d-block mb-2"></i>
                                جدولي
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
