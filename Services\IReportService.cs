using SchoolManager.Models;

namespace SchoolManager.Services
{
    public interface IReportService
    {
        Task<byte[]> GenerateStudentReportCardAsync(int studentId, string semester, string academicYear);
        Task<byte[]> GenerateClassGradeReportAsync(int classId, string semester, string academicYear);
        Task<byte[]> GenerateAttendanceReportAsync(int studentId, DateTime startDate, DateTime endDate);
        Task<byte[]> GenerateClassAttendanceReportAsync(int classId, DateTime startDate, DateTime endDate);
        Task<byte[]> GenerateFinancialReportAsync(DateTime startDate, DateTime endDate);
        Task<byte[]> GenerateTeacherScheduleAsync(int teacherId, string semester, string academicYear);
        Task<byte[]> GenerateClassScheduleAsync(int classId, string semester, string academicYear);
        Task<byte[]> GenerateStudentListAsync(int classId);
        Task<byte[]> GeneratePaymentReportAsync(int studentId, string academicYear);
        Task<byte[]> GenerateOverduePaymentsReportAsync();
    }
}
