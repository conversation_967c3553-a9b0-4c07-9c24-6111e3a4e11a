namespace SchoolManager.Services
{
    public interface IEmailService
    {
        Task SendEmailAsync(string to, string subject, string body, bool isHtml = true);
        Task SendEmailAsync(List<string> to, string subject, string body, bool isHtml = true);
        Task SendGradeReportAsync(string studentEmail, string studentName, string reportContent);
        Task SendAttendanceNotificationAsync(string parentEmail, string studentName, string message);
        Task SendPaymentReminderAsync(string parentEmail, string studentName, decimal amount, DateTime dueDate);
        Task SendWelcomeEmailAsync(string email, string name, string role, string temporaryPassword);
    }
}
