using Microsoft.AspNetCore.Identity;
using SchoolManager.Models;

namespace SchoolManager.Data
{
    public static class DbInitializer
    {
        public static async Task Initialize(SchoolContext context, UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager)
        {
            // Ensure database is created
            await context.Database.EnsureCreatedAsync();

            // Create roles if they don't exist
            string[] roles = { "Admin", "Teacher", "Student", "Parent" };
            
            foreach (string role in roles)
            {
                if (!await roleManager.RoleExistsAsync(role))
                {
                    await roleManager.CreateAsync(new IdentityRole(role));
                }
            }

            // Create admin user if it doesn't exist
            var adminEmail = "<EMAIL>";
            var adminUser = await userManager.FindByEmailAsync(adminEmail);
            
            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    FirstName = "مدير",
                    LastName = "النظام",
                    EmailConfirmed = true,
                    IsActive = true,
                    Gender = Gender.Male,
                    DateOfBirth = new DateTime(1980, 1, 1)
                };

                var result = await userManager.CreateAsync(adminUser, "Admin123!");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }

            // Seed sample data if database is empty
            if (!context.Classes.Any())
            {
                await SeedSampleData(context, userManager);
            }
        }

        private static async Task SeedSampleData(SchoolContext context, UserManager<ApplicationUser> userManager)
        {
            // Create sample subjects
            var subjects = new List<Subject>
            {
                new Subject { Name = "الرياضيات", Code = "MATH101", CreditHours = 4, Type = SubjectType.Core, Department = "العلوم" },
                new Subject { Name = "اللغة العربية", Code = "ARAB101", CreditHours = 3, Type = SubjectType.Core, Department = "اللغات" },
                new Subject { Name = "اللغة الإنجليزية", Code = "ENG101", CreditHours = 3, Type = SubjectType.Core, Department = "اللغات" },
                new Subject { Name = "العلوم", Code = "SCI101", CreditHours = 4, Type = SubjectType.Core, Department = "العلوم" },
                new Subject { Name = "التاريخ", Code = "HIST101", CreditHours = 2, Type = SubjectType.Core, Department = "الاجتماعيات" },
                new Subject { Name = "الجغرافيا", Code = "GEO101", CreditHours = 2, Type = SubjectType.Core, Department = "الاجتماعيات" },
                new Subject { Name = "التربية الإسلامية", Code = "REL101", CreditHours = 2, Type = SubjectType.Core, Department = "الدين" },
                new Subject { Name = "التربية البدنية", Code = "PE101", CreditHours = 1, Type = SubjectType.Elective, Department = "الرياضة" }
            };

            context.Subjects.AddRange(subjects);
            await context.SaveChangesAsync();

            // Create sample classes
            var classes = new List<Class>
            {
                new Class { Name = "الصف الأول أ", Grade = "الأول", Section = "أ", Capacity = 30, Classroom = "101" },
                new Class { Name = "الصف الأول ب", Grade = "الأول", Section = "ب", Capacity = 30, Classroom = "102" },
                new Class { Name = "الصف الثاني أ", Grade = "الثاني", Section = "أ", Capacity = 30, Classroom = "201" },
                new Class { Name = "الصف الثاني ب", Grade = "الثاني", Section = "ب", Capacity = 30, Classroom = "202" },
                new Class { Name = "الصف الثالث أ", Grade = "الثالث", Section = "أ", Capacity = 30, Classroom = "301" }
            };

            context.Classes.AddRange(classes);
            await context.SaveChangesAsync();

            // Create sample teacher user
            var teacherUser = new ApplicationUser
            {
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "أحمد",
                LastName = "محمد",
                EmailConfirmed = true,
                IsActive = true,
                Gender = Gender.Male,
                DateOfBirth = new DateTime(1985, 5, 15),
                NationalId = "1234567890"
            };

            var teacherResult = await userManager.CreateAsync(teacherUser, "Teacher123!");
            if (teacherResult.Succeeded)
            {
                await userManager.AddToRoleAsync(teacherUser, "Teacher");

                // Create teacher record
                var teacher = new Teacher
                {
                    UserId = teacherUser.Id,
                    EmployeeNumber = "T001",
                    Qualification = "بكالوريوس رياضيات",
                    Specialization = "الرياضيات",
                    Salary = 8000,
                    YearsOfExperience = 5
                };

                context.Teachers.Add(teacher);
                await context.SaveChangesAsync();
            }

            // Create sample student user
            var studentUser = new ApplicationUser
            {
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "فاطمة",
                LastName = "علي",
                EmailConfirmed = true,
                IsActive = true,
                Gender = Gender.Female,
                DateOfBirth = new DateTime(2010, 3, 20),
                NationalId = "0987654321"
            };

            var studentResult = await userManager.CreateAsync(studentUser, "Student123!");
            if (studentResult.Succeeded)
            {
                await userManager.AddToRoleAsync(studentUser, "Student");

                // Create student record
                var student = new Student
                {
                    UserId = studentUser.Id,
                    StudentNumber = "S001",
                    ClassId = classes.First().Id,
                    GuardianName = "علي أحمد",
                    GuardianPhone = "0501234567",
                    GuardianEmail = "<EMAIL>",
                    TotalFees = 5000,
                    PaidFees = 2500
                };

                context.Students.Add(student);
                await context.SaveChangesAsync();
            }
        }
    }
}
