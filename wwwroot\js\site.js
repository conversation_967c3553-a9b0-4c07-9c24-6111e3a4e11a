// School Management System - Custom JavaScript

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    $('.alert').each(function() {
        var alert = $(this);
        setTimeout(function() {
            alert.fadeOut('slow');
        }, 5000);
    });

    // Confirm delete actions
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        var form = $(this).closest('form');
        var itemName = $(this).data('item-name') || 'هذا العنصر';
        
        if (confirm('هل أنت متأكد من حذف ' + itemName + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
            form.submit();
        }
    });

    // Loading state for forms
    $('form').on('submit', function() {
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.text();
        
        submitBtn.prop('disabled', true)
                 .html('<span class="loading-spinner me-2"></span>جاري المعالجة...');
        
        // Re-enable button after 10 seconds as fallback
        setTimeout(function() {
            submitBtn.prop('disabled', false).text(originalText);
        }, 10000);
    });

    // Search functionality with debounce
    var searchTimeout;
    $('.search-input').on('input', function() {
        var searchTerm = $(this).val();
        var form = $(this).closest('form');
        
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            if (searchTerm.length >= 2 || searchTerm.length === 0) {
                form.submit();
            }
        }, 500);
    });

    // Auto-save draft functionality
    $('.auto-save').on('input', function() {
        var field = $(this);
        var fieldName = field.attr('name');
        var fieldValue = field.val();
        
        // Save to localStorage
        localStorage.setItem('draft_' + fieldName, fieldValue);
        
        // Show saved indicator
        field.addClass('border-success');
        setTimeout(function() {
            field.removeClass('border-success');
        }, 1000);
    });

    // Restore drafts on page load
    $('.auto-save').each(function() {
        var field = $(this);
        var fieldName = field.attr('name');
        var savedValue = localStorage.getItem('draft_' + fieldName);
        
        if (savedValue && field.val() === '') {
            field.val(savedValue);
            field.addClass('border-warning');
            
            // Add restore button
            field.after('<small class="text-warning">تم استعادة المسودة <button type="button" class="btn btn-sm btn-link p-0 clear-draft">مسح</button></small>');
        }
    });

    // Clear draft functionality
    $(document).on('click', '.clear-draft', function() {
        var field = $(this).closest('.form-group').find('.auto-save');
        var fieldName = field.attr('name');
        
        localStorage.removeItem('draft_' + fieldName);
        field.val('').removeClass('border-warning');
        $(this).parent().remove();
    });

    // Print functionality
    $('.btn-print').on('click', function() {
        window.print();
    });

    // Export functionality
    $('.btn-export').on('click', function() {
        var format = $(this).data('format') || 'pdf';
        var url = $(this).data('url');
        
        if (url) {
            window.open(url + '?format=' + format, '_blank');
        }
    });

    // Dynamic form validation
    $('form').each(function() {
        var form = $(this);
        
        form.find('input[required], select[required], textarea[required]').on('blur', function() {
            var field = $(this);
            var value = field.val().trim();
            
            if (value === '') {
                field.addClass('is-invalid');
                if (!field.next('.invalid-feedback').length) {
                    field.after('<div class="invalid-feedback">هذا الحقل مطلوب</div>');
                }
            } else {
                field.removeClass('is-invalid').addClass('is-valid');
                field.next('.invalid-feedback').remove();
            }
        });
    });

    // Email validation
    $('input[type="email"]').on('blur', function() {
        var email = $(this).val().trim();
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>');
            }
        } else if (email) {
            $(this).removeClass('is-invalid').addClass('is-valid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Phone number validation (Saudi format)
    $('input[type="tel"], .phone-input').on('blur', function() {
        var phone = $(this).val().trim();
        var phoneRegex = /^(05|5)[0-9]{8}$/;
        
        if (phone && !phoneRegex.test(phone)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">يرجى إدخال رقم جوال صحيح (05xxxxxxxx)</div>');
            }
        } else if (phone) {
            $(this).removeClass('is-invalid').addClass('is-valid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // National ID validation (Saudi format)
    $('.national-id-input').on('blur', function() {
        var nationalId = $(this).val().trim();
        var nationalIdRegex = /^[12][0-9]{9}$/;
        
        if (nationalId && !nationalIdRegex.test(nationalId)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">يرجى إدخال رقم هوية صحيح (10 أرقام)</div>');
            }
        } else if (nationalId) {
            $(this).removeClass('is-invalid').addClass('is-valid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Sidebar toggle for mobile
    $('.sidebar-toggle').on('click', function() {
        $('.sidebar').toggleClass('show');
    });

    // Close sidebar when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.sidebar, .sidebar-toggle').length) {
            $('.sidebar').removeClass('show');
        }
    });

    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        var target = $($(this).attr('href'));
        
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });

    // Back to top button
    $(window).scroll(function() {
        if ($(this).scrollTop() > 100) {
            $('.back-to-top').fadeIn();
        } else {
            $('.back-to-top').fadeOut();
        }
    });

    $('.back-to-top').on('click', function() {
        $('html, body').animate({scrollTop: 0}, 500);
    });

    // Data tables enhancement
    if ($.fn.DataTable) {
        $('.data-table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
            },
            responsive: true,
            pageLength: 25,
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: 'no-sort' }
            ]
        });
    }

    // Chart.js default configuration
    if (typeof Chart !== 'undefined') {
        Chart.defaults.font.family = 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif';
        Chart.defaults.color = '#6c757d';
        Chart.defaults.plugins.legend.rtl = true;
    }

    // Initialize date pickers
    if ($.fn.datepicker) {
        $('.date-picker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            todayHighlight: true,
            language: 'ar'
        });
    }

    // File upload preview
    $('.file-input').on('change', function() {
        var input = this;
        var preview = $(input).siblings('.file-preview');
        
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            
            reader.onload = function(e) {
                if (input.files[0].type.startsWith('image/')) {
                    preview.html('<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px;">');
                } else {
                    preview.html('<i class="fas fa-file me-2"></i>' + input.files[0].name);
                }
            };
            
            reader.readAsDataURL(input.files[0]);
        }
    });

    // Auto-resize textareas
    $('textarea').each(function() {
        this.setAttribute('style', 'height:' + (this.scrollHeight) + 'px;overflow-y:hidden;');
    }).on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});

// Utility functions
function showNotification(message, type = 'info') {
    var alertClass = 'alert-' + type;
    var icon = type === 'success' ? 'check-circle' : 
               type === 'error' ? 'exclamation-circle' : 
               type === 'warning' ? 'exclamation-triangle' : 'info-circle';
    
    var notification = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">' +
        '<i class="fas fa-' + icon + ' me-2"></i>' + message +
        '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
        '</div>');
    
    $('body').append(notification);
    
    setTimeout(function() {
        notification.fadeOut(function() {
            $(this).remove();
        });
    }, 5000);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA').format(new Date(date));
}

function validateForm(formSelector) {
    var form = $(formSelector);
    var isValid = true;
    
    form.find('input[required], select[required], textarea[required]').each(function() {
        var field = $(this);
        var value = field.val().trim();
        
        if (value === '') {
            field.addClass('is-invalid');
            isValid = false;
        } else {
            field.removeClass('is-invalid').addClass('is-valid');
        }
    });
    
    return isValid;
}
