namespace SchoolManager.Services
{
    public interface INotificationService
    {
        Task SendSmsAsync(string phoneNumber, string message);
        Task SendBulkSmsAsync(List<string> phoneNumbers, string message);
        Task NotifyParentAboutAbsenceAsync(int studentId, DateTime date);
        Task NotifyParentAboutGradesAsync(int studentId, string semester);
        Task NotifyAboutPaymentDueAsync(int studentId, decimal amount, DateTime dueDate);
        Task NotifyTeacherAboutScheduleChangeAsync(int teacherId, string message);
        Task SendEmergencyNotificationAsync(string message, List<string> recipients);
    }
}
