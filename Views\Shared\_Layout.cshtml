<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة المدرسة</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/SchoolManager.styles.css" asp-append-version="true" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
            color: #2c3e50 !important;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding-top: 20px;
        }
        .sidebar .nav-link {
            color: white;
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-graduation-cap me-2"></i>
                    نظام إدارة المدرسة
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                    </ul>
                    <partial name="_LoginPartial" />
                </div>
            </div>
        </nav>
    </header>

    <div class="container-fluid">
        <div class="row">
            @if (User.Identity?.IsAuthenticated ?? false)
            {
                <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Home" asp-action="Index">
                                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                </a>
                            </li>
                            
                            @if (User.IsInRole("Admin") || User.IsInRole("Teacher"))
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Students" asp-action="Index">
                                        <i class="fas fa-user-graduate me-2"></i>الطلاب
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Teachers" asp-action="Index">
                                        <i class="fas fa-chalkboard-teacher me-2"></i>المعلمين
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Classes" asp-action="Index">
                                        <i class="fas fa-school me-2"></i>الصفوف
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Subjects" asp-action="Index">
                                        <i class="fas fa-book me-2"></i>المواد
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Grades" asp-action="Index">
                                        <i class="fas fa-chart-line me-2"></i>الدرجات
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Attendance" asp-action="Index">
                                        <i class="fas fa-calendar-check me-2"></i>الحضور
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Schedule" asp-action="Index">
                                        <i class="fas fa-calendar-alt me-2"></i>الجدول
                                    </a>
                                </li>
                            }
                            
                            @if (User.IsInRole("Admin"))
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Payments" asp-action="Index">
                                        <i class="fas fa-money-bill-wave me-2"></i>المالية
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Reports" asp-action="Index">
                                        <i class="fas fa-file-alt me-2"></i>التقارير
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Settings" asp-action="Index">
                                        <i class="fas fa-cog me-2"></i>الإعدادات
                                    </a>
                                </li>
                            }
                        </ul>
                    </div>
                </nav>
                
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                    @if (TempData["Success"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>@TempData["Success"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    
                    @if (TempData["Error"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>@TempData["Error"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    
                    @RenderBody()
                </main>
            }
            else
            {
                <main class="col-12">
                    @RenderBody()
                </main>
            }
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
