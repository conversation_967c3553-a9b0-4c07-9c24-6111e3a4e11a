using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;
using Microsoft.Extensions.Options;

namespace SchoolManager.Services
{
    public class EmailSettings
    {
        public string SmtpServer { get; set; } = string.Empty;
        public int SmtpPort { get; set; }
        public string SenderEmail { get; set; } = string.Empty;
        public string SenderPassword { get; set; } = string.Empty;
        public string SenderName { get; set; } = string.Empty;
    }

    public class EmailService : IEmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly ILogger<EmailService> _logger;

        public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
        {
            _emailSettings = configuration.GetSection("EmailSettings").Get<EmailSettings>() ?? new EmailSettings();
            _logger = logger;
        }

        public async Task SendEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            await SendEmailAsync(new List<string> { to }, subject, body, isHtml);
        }

        public async Task SendEmailAsync(List<string> to, string subject, string body, bool isHtml = true)
        {
            try
            {
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress(_emailSettings.SenderName, _emailSettings.SenderEmail));
                
                foreach (var recipient in to)
                {
                    message.To.Add(new MailboxAddress("", recipient));
                }

                message.Subject = subject;

                var bodyBuilder = new BodyBuilder();
                if (isHtml)
                {
                    bodyBuilder.HtmlBody = body;
                }
                else
                {
                    bodyBuilder.TextBody = body;
                }

                message.Body = bodyBuilder.ToMessageBody();

                using var client = new SmtpClient();
                await client.ConnectAsync(_emailSettings.SmtpServer, _emailSettings.SmtpPort, SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(_emailSettings.SenderEmail, _emailSettings.SenderPassword);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);

                _logger.LogInformation($"Email sent successfully to {string.Join(", ", to)}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send email to {string.Join(", ", to)}");
                throw;
            }
        }

        public async Task SendGradeReportAsync(string studentEmail, string studentName, string reportContent)
        {
            var subject = "تقرير الدرجات - Grade Report";
            var body = $@"
                <div dir='rtl' style='font-family: Arial, sans-serif;'>
                    <h2>عزيزي/عزيزتي {studentName}</h2>
                    <p>نرفق لكم تقرير الدرجات الخاص بكم:</p>
                    <div style='background-color: #f5f5f5; padding: 15px; border-radius: 5px;'>
                        {reportContent}
                    </div>
                    <p>مع تحيات إدارة المدرسة</p>
                </div>";

            await SendEmailAsync(studentEmail, subject, body);
        }

        public async Task SendAttendanceNotificationAsync(string parentEmail, string studentName, string message)
        {
            var subject = "إشعار حضور - Attendance Notification";
            var body = $@"
                <div dir='rtl' style='font-family: Arial, sans-serif;'>
                    <h2>ولي أمر الطالب/ة {studentName}</h2>
                    <p>{message}</p>
                    <p>للاستفسار يرجى التواصل مع إدارة المدرسة</p>
                    <p>مع تحيات إدارة المدرسة</p>
                </div>";

            await SendEmailAsync(parentEmail, subject, body);
        }

        public async Task SendPaymentReminderAsync(string parentEmail, string studentName, decimal amount, DateTime dueDate)
        {
            var subject = "تذكير بالدفع - Payment Reminder";
            var body = $@"
                <div dir='rtl' style='font-family: Arial, sans-serif;'>
                    <h2>ولي أمر الطالب/ة {studentName}</h2>
                    <p>نذكركم بوجود مبلغ مستحق للدفع:</p>
                    <ul>
                        <li>المبلغ: {amount:C} ريال</li>
                        <li>تاريخ الاستحقاق: {dueDate:yyyy/MM/dd}</li>
                    </ul>
                    <p>يرجى المبادرة بالدفع لتجنب أي رسوم إضافية</p>
                    <p>مع تحيات إدارة المدرسة</p>
                </div>";

            await SendEmailAsync(parentEmail, subject, body);
        }

        public async Task SendWelcomeEmailAsync(string email, string name, string role, string temporaryPassword)
        {
            var subject = "مرحباً بك في نظام إدارة المدرسة";
            var body = $@"
                <div dir='rtl' style='font-family: Arial, sans-serif;'>
                    <h2>مرحباً {name}</h2>
                    <p>تم إنشاء حساب جديد لك في نظام إدارة المدرسة</p>
                    <p><strong>الدور:</strong> {role}</p>
                    <p><strong>البريد الإلكتروني:</strong> {email}</p>
                    <p><strong>كلمة المرور المؤقتة:</strong> {temporaryPassword}</p>
                    <p style='color: red;'><strong>مهم:</strong> يرجى تغيير كلمة المرور عند أول تسجيل دخول</p>
                    <p>مع تحيات إدارة المدرسة</p>
                </div>";

            await SendEmailAsync(email, subject, body);
        }
    }
}
