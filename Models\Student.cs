using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SchoolManager.Models
{
    public class Student
    {
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        public string StudentNumber { get; set; } = string.Empty;

        [Required]
        public string UserId { get; set; } = string.Empty;
        public ApplicationUser User { get; set; } = null!;

        public int ClassId { get; set; }
        public Class Class { get; set; } = null!;

        [Required]
        [StringLength(100)]
        public string GuardianName { get; set; } = string.Empty;

        [Required]
        [StringLength(15)]
        public string GuardianPhone { get; set; } = string.Empty;

        [StringLength(100)]
        public string? GuardianEmail { get; set; }

        [StringLength(200)]
        public string? GuardianAddress { get; set; }

        public StudentStatus Status { get; set; } = StudentStatus.Active;

        public DateTime EnrollmentDate { get; set; } = DateTime.UtcNow;

        public DateTime? GraduationDate { get; set; }

        [StringLength(500)]
        public string? MedicalNotes { get; set; }

        [StringLength(500)]
        public string? BehaviorNotes { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal TotalFees { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal PaidFees { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal RemainingFees => TotalFees - PaidFees;

        // Navigation properties
        public ICollection<Grade> Grades { get; set; } = new List<Grade>();
        public ICollection<Attendance> Attendances { get; set; } = new List<Attendance>();
        public ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public ICollection<Discipline> DisciplineRecords { get; set; } = new List<Discipline>();
    }

    public enum StudentStatus
    {
        Active = 1,
        Inactive = 2,
        Graduated = 3,
        Transferred = 4,
        Suspended = 5
    }

    public class Discipline
    {
        public int Id { get; set; }
        public int StudentId { get; set; }
        public Student Student { get; set; } = null!;
        
        [Required]
        [StringLength(200)]
        public string Violation { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public DisciplineType Type { get; set; }
        
        [StringLength(500)]
        public string? Action { get; set; }
        
        public DateTime Date { get; set; } = DateTime.UtcNow;
        
        [StringLength(100)]
        public string ReportedBy { get; set; } = string.Empty;
    }

    public enum DisciplineType
    {
        Warning = 1,
        Detention = 2,
        Suspension = 3,
        Expulsion = 4
    }
}
