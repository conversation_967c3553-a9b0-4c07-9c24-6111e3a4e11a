using System.ComponentModel.DataAnnotations;

namespace SchoolManager.Models
{
    public class Class
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        public string Grade { get; set; } = string.Empty; // الصف الأول، الثاني، إلخ

        [StringLength(10)]
        public string? Section { get; set; } // أ، ب، ج

        public int Capacity { get; set; } = 30;

        [StringLength(50)]
        public string? Classroom { get; set; }

        public int? ClassTeacherId { get; set; }
        public Teacher? ClassTeacher { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(500)]
        public string? Description { get; set; }

        // Navigation properties
        public ICollection<Student> Students { get; set; } = new List<Student>();
        public ICollection<SubjectTeacher> SubjectTeachers { get; set; } = new List<SubjectTeacher>();
        public ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();
        public ICollection<ClassSubject> ClassSubjects { get; set; } = new List<ClassSubject>();

        public int CurrentStudentCount => Students.Count(s => s.Status == StudentStatus.Active);
        public bool IsFull => CurrentStudentCount >= Capacity;
        public string FullName => $"{Grade} - {Section}";
    }

    public class ClassSubject
    {
        public int Id { get; set; }
        public int ClassId { get; set; }
        public Class Class { get; set; } = null!;
        public int SubjectId { get; set; }
        public Subject Subject { get; set; } = null!;
        public int WeeklyHours { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
    }
}
