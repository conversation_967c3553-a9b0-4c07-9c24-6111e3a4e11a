using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SchoolManager.Models
{
    public class Subject
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string Code { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public int CreditHours { get; set; } = 1;

        public SubjectType Type { get; set; } = SubjectType.Core;

        public bool IsActive { get; set; } = true;

        [StringLength(100)]
        public string? Department { get; set; }

        [StringLength(500)]
        public string? Prerequisites { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Grade distribution settings
        public int AttendanceWeight { get; set; } = 10; // %
        public int AssignmentWeight { get; set; } = 20; // %
        public int MidtermWeight { get; set; } = 30; // %
        public int FinalWeight { get; set; } = 40; // %

        // Navigation properties
        public ICollection<SubjectTeacher> SubjectTeachers { get; set; } = new List<SubjectTeacher>();
        public ICollection<Grade> Grades { get; set; } = new List<Grade>();
        public ICollection<ClassSubject> ClassSubjects { get; set; } = new List<ClassSubject>();
        public ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();
    }

    public enum SubjectType
    {
        Core = 1,        // مواد أساسية
        Elective = 2,    // مواد اختيارية
        Extracurricular = 3 // أنشطة لا منهجية
    }
}
