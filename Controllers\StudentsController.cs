using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using SchoolManager.Data;
using SchoolManager.Models;
using SchoolManager.Services;

namespace SchoolManager.Controllers
{
    [Authorize(Roles = "Admin,Teacher")]
    public class StudentsController : Controller
    {
        private readonly SchoolContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IEmailService _emailService;
        private readonly INotificationService _notificationService;

        public StudentsController(
            SchoolContext context,
            UserManager<ApplicationUser> userManager,
            IEmailService emailService,
            INotificationService notificationService)
        {
            _context = context;
            _userManager = userManager;
            _emailService = emailService;
            _notificationService = notificationService;
        }

        // GET: Students
        public async Task<IActionResult> Index(string searchString, int? classId, StudentStatus? status, int page = 1, int pageSize = 20)
        {
            var query = _context.Students
                .Include(s => s.User)
                .Include(s => s.Class)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(searchString))
            {
                query = query.Where(s => s.User.FirstName.Contains(searchString) ||
                                        s.User.LastName.Contains(searchString) ||
                                        s.StudentNumber.Contains(searchString) ||
                                        s.User.NationalId!.Contains(searchString));
            }

            if (classId.HasValue)
            {
                query = query.Where(s => s.ClassId == classId.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(s => s.Status == status.Value);
            }

            // Pagination
            var totalCount = await query.CountAsync();
            var students = await query
                .OrderBy(s => s.User.FirstName)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Prepare view data
            ViewBag.Classes = new SelectList(await _context.Classes.Where(c => c.IsActive).ToListAsync(), "Id", "FullName", classId);
            ViewBag.Statuses = new SelectList(Enum.GetValues<StudentStatus>().Select(s => new { Value = (int)s, Text = s.ToString() }), "Value", "Text", (int?)status);
            ViewBag.SearchString = searchString;
            ViewBag.CurrentPage = page;
            ViewBag.TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            ViewBag.TotalCount = totalCount;

            return View(students);
        }

        // GET: Students/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null) return NotFound();

            var student = await _context.Students
                .Include(s => s.User)
                .Include(s => s.Class)
                .Include(s => s.Grades)
                    .ThenInclude(g => g.Subject)
                .Include(s => s.Attendances.OrderByDescending(a => a.Date).Take(10))
                .Include(s => s.Payments.OrderByDescending(p => p.CreatedAt).Take(10))
                .Include(s => s.DisciplineRecords.OrderByDescending(d => d.Date).Take(5))
                .FirstOrDefaultAsync(s => s.Id == id);

            if (student == null) return NotFound();

            // Calculate statistics
            var currentSemester = "الفصل الأول"; // This should be dynamic
            var currentGrades = student.Grades.Where(g => g.Semester == currentSemester).ToList();
            
            ViewBag.CurrentGPA = currentGrades.Any() ? currentGrades.Average(g => g.GPA) : 0;
            ViewBag.TotalSubjects = currentGrades.Count;
            ViewBag.PassingSubjects = currentGrades.Count(g => g.IsPassing);

            // Attendance statistics for current month
            var thisMonth = DateTime.Now.Month;
            var thisYear = DateTime.Now.Year;
            var monthlyAttendance = student.Attendances
                .Where(a => a.Date.Month == thisMonth && a.Date.Year == thisYear)
                .ToList();

            ViewBag.MonthlyAttendancePercentage = monthlyAttendance.Any() 
                ? (double)monthlyAttendance.Count(a => a.Status == AttendanceStatus.Present) / monthlyAttendance.Count * 100 
                : 0;

            return View(student);
        }

        // GET: Students/Create
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Create()
        {
            ViewBag.Classes = new SelectList(await _context.Classes.Where(c => c.IsActive).ToListAsync(), "Id", "FullName");
            return View();
        }

        // POST: Students/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Create(StudentCreateViewModel model)
        {
            if (ModelState.IsValid)
            {
                // Create user account
                var user = new ApplicationUser
                {
                    UserName = model.Email,
                    Email = model.Email,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    NationalId = model.NationalId,
                    Address = model.Address,
                    DateOfBirth = model.DateOfBirth,
                    Gender = model.Gender,
                    PhoneNumber = model.PhoneNumber,
                    EmailConfirmed = true,
                    IsActive = true
                };

                var temporaryPassword = GenerateTemporaryPassword();
                var result = await _userManager.CreateAsync(user, temporaryPassword);

                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(user, "Student");

                    // Create student record
                    var student = new Student
                    {
                        UserId = user.Id,
                        StudentNumber = await GenerateStudentNumber(),
                        ClassId = model.ClassId,
                        GuardianName = model.GuardianName,
                        GuardianPhone = model.GuardianPhone,
                        GuardianEmail = model.GuardianEmail,
                        GuardianAddress = model.GuardianAddress,
                        TotalFees = model.TotalFees,
                        MedicalNotes = model.MedicalNotes
                    };

                    _context.Students.Add(student);
                    await _context.SaveChangesAsync();

                    // Send welcome email
                    if (!string.IsNullOrEmpty(model.Email))
                    {
                        await _emailService.SendWelcomeEmailAsync(model.Email, user.FullName, "Student", temporaryPassword);
                    }

                    TempData["Success"] = "تم إنشاء حساب الطالب بنجاح";
                    return RedirectToAction(nameof(Details), new { id = student.Id });
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            ViewBag.Classes = new SelectList(await _context.Classes.Where(c => c.IsActive).ToListAsync(), "Id", "FullName", model.ClassId);
            return View(model);
        }

        // GET: Students/Edit/5
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null) return NotFound();

            var student = await _context.Students
                .Include(s => s.User)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (student == null) return NotFound();

            var model = new StudentEditViewModel
            {
                Id = student.Id,
                FirstName = student.User.FirstName,
                LastName = student.User.LastName,
                Email = student.User.Email!,
                NationalId = student.User.NationalId,
                Address = student.User.Address,
                DateOfBirth = student.User.DateOfBirth,
                Gender = student.User.Gender,
                PhoneNumber = student.User.PhoneNumber,
                ClassId = student.ClassId,
                GuardianName = student.GuardianName,
                GuardianPhone = student.GuardianPhone,
                GuardianEmail = student.GuardianEmail,
                GuardianAddress = student.GuardianAddress,
                Status = student.Status,
                TotalFees = student.TotalFees,
                PaidFees = student.PaidFees,
                MedicalNotes = student.MedicalNotes,
                BehaviorNotes = student.BehaviorNotes
            };

            ViewBag.Classes = new SelectList(await _context.Classes.Where(c => c.IsActive).ToListAsync(), "Id", "FullName", student.ClassId);
            return View(model);
        }

        // POST: Students/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Edit(int id, StudentEditViewModel model)
        {
            if (id != model.Id) return NotFound();

            if (ModelState.IsValid)
            {
                var student = await _context.Students
                    .Include(s => s.User)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (student == null) return NotFound();

                // Update user information
                student.User.FirstName = model.FirstName;
                student.User.LastName = model.LastName;
                student.User.Email = model.Email;
                student.User.UserName = model.Email;
                student.User.NationalId = model.NationalId;
                student.User.Address = model.Address;
                student.User.DateOfBirth = model.DateOfBirth;
                student.User.Gender = model.Gender;
                student.User.PhoneNumber = model.PhoneNumber;

                // Update student information
                student.ClassId = model.ClassId;
                student.GuardianName = model.GuardianName;
                student.GuardianPhone = model.GuardianPhone;
                student.GuardianEmail = model.GuardianEmail;
                student.GuardianAddress = model.GuardianAddress;
                student.Status = model.Status;
                student.TotalFees = model.TotalFees;
                student.PaidFees = model.PaidFees;
                student.MedicalNotes = model.MedicalNotes;
                student.BehaviorNotes = model.BehaviorNotes;

                await _context.SaveChangesAsync();

                TempData["Success"] = "تم تحديث بيانات الطالب بنجاح";
                return RedirectToAction(nameof(Details), new { id = student.Id });
            }

            ViewBag.Classes = new SelectList(await _context.Classes.Where(c => c.IsActive).ToListAsync(), "Id", "FullName", model.ClassId);
            return View(model);
        }

        private async Task<string> GenerateStudentNumber()
        {
            var year = DateTime.Now.Year.ToString().Substring(2);
            var lastStudent = await _context.Students
                .Where(s => s.StudentNumber.StartsWith(year))
                .OrderByDescending(s => s.StudentNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastStudent != null && int.TryParse(lastStudent.StudentNumber.Substring(2), out int lastNumber))
            {
                nextNumber = lastNumber + 1;
            }

            return $"{year}{nextNumber:D4}";
        }

        private string GenerateTemporaryPassword()
        {
            var random = new Random();
            return $"Student{random.Next(1000, 9999)}!";
        }
    }

    // View Models
    public class StudentCreateViewModel
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? NationalId { get; set; }
        public string? Address { get; set; }
        public DateTime DateOfBirth { get; set; }
        public Gender Gender { get; set; }
        public string? PhoneNumber { get; set; }
        public int ClassId { get; set; }
        public string GuardianName { get; set; } = string.Empty;
        public string GuardianPhone { get; set; } = string.Empty;
        public string? GuardianEmail { get; set; }
        public string? GuardianAddress { get; set; }
        public decimal TotalFees { get; set; }
        public string? MedicalNotes { get; set; }
    }

    public class StudentEditViewModel : StudentCreateViewModel
    {
        public int Id { get; set; }
        public StudentStatus Status { get; set; }
        public decimal PaidFees { get; set; }
        public string? BehaviorNotes { get; set; }
    }
}
